#!/usr/bin/env python3
"""
Script para análise de correlação entre Volume e Tendência (variação diária de preços)
das 20 ações diversificadas do arquivo acoes_diversificadas.csv
"""

import yfinance as yf
import matplotlib.pyplot as plt
import pandas as pd
import numpy as np
import seaborn as sns
import warnings
import os
from scipy import stats
from datetime import datetime, timedelta
warnings.filterwarnings('ignore')

# Configurar matplotlib
import matplotlib
matplotlib.use('Agg')  # Backend para salvar sem display

def carregar_acoes_diversificadas():
    """
    Carrega as 20 ações do arquivo CSV de diversificação
    """
    try:
        csv_path = 'results/csv/correlation_data/acoes_diversificacao.csv'
        df = pd.read_csv(csv_path)
        
        # Pegar apenas as primeiras 20 ações (excluindo linha vazia no final)
        acoes = []
        for _, row in df.head(20).iterrows():
            ticker = row['Ticker'] + '.SA'
            nome = row['Nome']
            acoes.append((ticker, nome))
        
        print(f"📋 Carregadas {len(acoes)} ações diversificadas do arquivo CSV")
        return acoes
        
    except Exception as e:
        print(f"❌ Erro ao carregar arquivo CSV: {e}")
        return []

def obter_dados_volume_tendencia(ticker, nome, periodo="12mo"):
    """
    Obtém dados de preços e calcula volume e tendência diária
    """
    try:
        print(f"  📊 {ticker.replace('.SA', ''):8s} - {nome}")
        
        stock = yf.Ticker(ticker)
        dados = stock.history(period=periodo)
        
        if dados.empty or len(dados) < 30:
            print(f"     ⚠️ Dados insuficientes ({len(dados) if not dados.empty else 0} dias)")
            return None
        
        # Calcular tendência (variação percentual diária)
        dados['Tendencia'] = dados['Close'].pct_change() * 100
        
        # Normalizar volume (para comparação entre ações)
        dados['Volume_Normalizado'] = (dados['Volume'] - dados['Volume'].mean()) / dados['Volume'].std()
        
        # Calcular variação absoluta de preço
        dados['Variacao_Absoluta'] = dados['Close'].diff()
        
        # Remover valores NaN
        dados = dados.dropna()
        
        print(f"     ✅ {len(dados)} dias processados")
        
        return dados
        
    except Exception as e:
        print(f"     ❌ Erro: {str(e)[:50]}")
        return None

def calcular_correlacoes(dados, ticker, nome):
    """
    Calcula correlações entre volume e diferentes métricas de tendência
    """
    correlacoes = {}
    
    # Correlação Volume vs Tendência (%)
    corr_tendencia, p_val_tendencia = stats.pearsonr(dados['Volume'], dados['Tendencia'])
    correlacoes['volume_vs_tendencia'] = {
        'correlacao': corr_tendencia,
        'p_valor': p_val_tendencia,
        'significativo': p_val_tendencia < 0.05
    }
    
    # Correlação Volume vs Tendência Absoluta (valor absoluto da variação %)
    corr_abs_tendencia, p_val_abs = stats.pearsonr(dados['Volume'], np.abs(dados['Tendencia']))
    correlacoes['volume_vs_tendencia_abs'] = {
        'correlacao': corr_abs_tendencia,
        'p_valor': p_val_abs,
        'significativo': p_val_abs < 0.05
    }
    
    # Correlação Volume vs Variação Absoluta de Preço
    corr_var_abs, p_val_var = stats.pearsonr(dados['Volume'], np.abs(dados['Variacao_Absoluta']))
    correlacoes['volume_vs_variacao_abs'] = {
        'correlacao': corr_var_abs,
        'p_valor': p_val_var,
        'significativo': p_val_var < 0.05
    }
    
    return correlacoes

def criar_grafico_correlacao_individual(dados, ticker, nome, correlacoes):
    """
    Cria gráfico de correlação individual para uma ação
    """
    fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(16, 12))
    
    # Gráfico 1: Volume vs Tendência (scatter plot)
    ax1.scatter(dados['Volume']/1e6, dados['Tendencia'], alpha=0.6, s=20)
    ax1.set_xlabel('Volume (Milhões)')
    ax1.set_ylabel('Tendência (%)')
    ax1.set_title(f'Volume vs Tendência\nCorr: {correlacoes["volume_vs_tendencia"]["correlacao"]:.3f}')
    ax1.grid(True, alpha=0.3)
    
    # Linha de tendência
    z = np.polyfit(dados['Volume']/1e6, dados['Tendencia'], 1)
    p = np.poly1d(z)
    ax1.plot(dados['Volume']/1e6, p(dados['Volume']/1e6), "r--", alpha=0.8)
    
    # Gráfico 2: Volume vs Tendência Absoluta
    ax2.scatter(dados['Volume']/1e6, np.abs(dados['Tendencia']), alpha=0.6, s=20, color='orange')
    ax2.set_xlabel('Volume (Milhões)')
    ax2.set_ylabel('|Tendência| (%)')
    ax2.set_title(f'Volume vs |Tendência|\nCorr: {correlacoes["volume_vs_tendencia_abs"]["correlacao"]:.3f}')
    ax2.grid(True, alpha=0.3)
    
    # Linha de tendência
    z2 = np.polyfit(dados['Volume']/1e6, np.abs(dados['Tendencia']), 1)
    p2 = np.poly1d(z2)
    ax2.plot(dados['Volume']/1e6, p2(dados['Volume']/1e6), "r--", alpha=0.8)
    
    # Gráfico 3: Série temporal do Volume
    ax3.plot(dados.index, dados['Volume']/1e6, color='purple', alpha=0.7)
    ax3.set_xlabel('Data')
    ax3.set_ylabel('Volume (Milhões)')
    ax3.set_title('Evolução do Volume')
    ax3.grid(True, alpha=0.3)
    
    # Gráfico 4: Série temporal da Tendência
    ax4.plot(dados.index, dados['Tendencia'], color='green', alpha=0.7)
    ax4.axhline(y=0, color='black', linestyle='-', alpha=0.5)
    ax4.set_xlabel('Data')
    ax4.set_ylabel('Tendência (%)')
    ax4.set_title('Evolução da Tendência')
    ax4.grid(True, alpha=0.3)
    
    plt.suptitle(f'{nome} ({ticker.replace(".SA", "")}) - Análise Volume vs Tendência', 
                 fontsize=16, fontweight='bold')
    plt.tight_layout()
    
    # Salvar gráfico
    os.makedirs('results/figures/volume_tendencia', exist_ok=True)
    nome_arquivo = f"results/figures/volume_tendencia/vol_tend_{ticker.replace('.SA', '')}.png"
    plt.savefig(nome_arquivo, dpi=300, bbox_inches='tight')
    plt.close()
    
    print(f"     💾 {nome_arquivo}")
    
    return {
        'ticker': ticker,
        'nome': nome,
        'correlacao_tendencia': correlacoes['volume_vs_tendencia']['correlacao'],
        'p_valor_tendencia': correlacoes['volume_vs_tendencia']['p_valor'],
        'significativo_tendencia': correlacoes['volume_vs_tendencia']['significativo'],
        'correlacao_abs': correlacoes['volume_vs_tendencia_abs']['correlacao'],
        'p_valor_abs': correlacoes['volume_vs_tendencia_abs']['p_valor'],
        'significativo_abs': correlacoes['volume_vs_tendencia_abs']['significativo'],
        'correlacao_var_abs': correlacoes['volume_vs_variacao_abs']['correlacao'],
        'volume_medio': dados['Volume'].mean(),
        'tendencia_media': dados['Tendencia'].mean(),
        'volatilidade': dados['Tendencia'].std()
    }

def criar_dashboard_correlacoes(resultados):
    """
    Cria dashboard consolidado das correlações
    """
    print("\n📊 Criando dashboard de correlações...")
    
    fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(20, 16))
    
    # Preparar dados
    tickers = [r['ticker'].replace('.SA', '') for r in resultados]
    corr_tendencia = [r['correlacao_tendencia'] for r in resultados]
    corr_abs = [r['correlacao_abs'] for r in resultados]
    significativos = [r['significativo_tendencia'] for r in resultados]
    
    # Gráfico 1: Correlações Volume vs Tendência
    cores = ['green' if sig else 'red' for sig in significativos]
    bars1 = ax1.bar(range(len(tickers)), corr_tendencia, color=cores, alpha=0.7)
    ax1.set_title('Correlação Volume vs Tendência', fontsize=14, fontweight='bold')
    ax1.set_xlabel('Ações')
    ax1.set_ylabel('Correlação')
    ax1.set_xticks(range(len(tickers)))
    ax1.set_xticklabels(tickers, rotation=45, ha='right')
    ax1.axhline(y=0, color='black', linestyle='-', alpha=0.5)
    ax1.grid(True, alpha=0.3, axis='y')
    
    # Gráfico 2: Correlações Volume vs |Tendência|
    cores_abs = ['darkgreen' if r['significativo_abs'] else 'darkred' for r in resultados]
    bars2 = ax2.bar(range(len(tickers)), corr_abs, color=cores_abs, alpha=0.7)
    ax2.set_title('Correlação Volume vs |Tendência|', fontsize=14, fontweight='bold')
    ax2.set_xlabel('Ações')
    ax2.set_ylabel('Correlação')
    ax2.set_xticks(range(len(tickers)))
    ax2.set_xticklabels(tickers, rotation=45, ha='right')
    ax2.axhline(y=0, color='black', linestyle='-', alpha=0.5)
    ax2.grid(True, alpha=0.3, axis='y')
    
    # Gráfico 3: Scatter plot geral - Correlação vs Volatilidade
    volatilidades = [r['volatilidade'] for r in resultados]
    scatter = ax3.scatter(volatilidades, corr_abs, c=corr_tendencia, 
                         cmap='RdYlGn', s=100, alpha=0.7)
    ax3.set_xlabel('Volatilidade (%)')
    ax3.set_ylabel('Correlação Volume vs |Tendência|')
    ax3.set_title('Volatilidade vs Correlação Volume-Tendência', fontsize=14, fontweight='bold')
    ax3.grid(True, alpha=0.3)
    plt.colorbar(scatter, ax=ax3, label='Corr Volume vs Tendência')
    
    # Gráfico 4: Distribuição das correlações
    ax4.hist(corr_tendencia, bins=10, alpha=0.7, color='blue', label='Volume vs Tendência')
    ax4.hist(corr_abs, bins=10, alpha=0.7, color='orange', label='Volume vs |Tendência|')
    ax4.set_xlabel('Correlação')
    ax4.set_ylabel('Frequência')
    ax4.set_title('Distribuição das Correlações', fontsize=14, fontweight='bold')
    ax4.legend()
    ax4.grid(True, alpha=0.3)
    
    plt.suptitle('Dashboard - Análise Volume vs Tendência', fontsize=18, fontweight='bold')
    plt.tight_layout()
    
    # Salvar dashboard
    os.makedirs('results/figures/dashboards', exist_ok=True)
    plt.savefig('results/figures/dashboards/dashboard_volume_tendencia.png', dpi=300, bbox_inches='tight')
    plt.close()
    
    print("   💾 results/figures/dashboards/dashboard_volume_tendencia.png")

def criar_heatmap_correlacoes(resultados):
    """
    Cria heatmap das correlações entre todas as ações
    """
    print("\n🔥 Criando heatmap de correlações...")

    # Preparar dados para heatmap
    tickers = [r['ticker'].replace('.SA', '') for r in resultados]

    # Criar matriz de dados
    dados_matriz = []
    for r in resultados:
        dados_matriz.append([
            r['correlacao_tendencia'],
            r['correlacao_abs'],
            r['correlacao_var_abs'],
            r['volatilidade'],
            r['volume_medio']/1e6  # Volume em milhões
        ])

    df_heatmap = pd.DataFrame(dados_matriz,
                             index=tickers,
                             columns=['Vol vs Tend', 'Vol vs |Tend|', 'Vol vs |Var|',
                                    'Volatilidade', 'Vol Médio (M)'])

    # Criar heatmap
    plt.figure(figsize=(12, 16))
    sns.heatmap(df_heatmap, annot=True, cmap='RdYlGn', center=0,
                fmt='.3f', cbar_kws={'label': 'Valor'})
    plt.title('Heatmap - Correlações Volume vs Tendência', fontsize=16, fontweight='bold')
    plt.tight_layout()

    # Salvar heatmap
    plt.savefig('results/figures/dashboards/heatmap_volume_tendencia.png', dpi=300, bbox_inches='tight')
    plt.close()

    print("   💾 results/figures/dashboards/heatmap_volume_tendencia.png")

def gerar_relatorio_correlacoes(resultados):
    """
    Gera relatório detalhado das correlações
    """
    print("\n" + "="*120)
    print("📈 RELATÓRIO ANÁLISE CORRELAÇÃO VOLUME vs TENDÊNCIA")
    print("="*120)

    # Ordenar por correlação absoluta (volume vs |tendência|)
    resultados_ord = sorted(resultados, key=lambda x: x['correlacao_abs'], reverse=True)

    print(f"{'#':<3} {'Ticker':<8} {'Nome':<20} {'Vol-Tend':<10} {'P-val':<8} {'Sig':<5} "
          f"{'Vol-|Tend|':<10} {'Volatil':<8} {'Vol Médio':<12}")
    print("-" * 120)

    for i, r in enumerate(resultados_ord, 1):
        sig_symbol = "✓" if r['significativo_tendencia'] else "✗"
        vol_medio_m = r['volume_medio'] / 1e6

        print(f"{i:<3} {r['ticker'].replace('.SA', ''):<8} "
              f"{r['nome'][:19]:<20} {r['correlacao_tendencia']:>+8.3f} "
              f"{r['p_valor_tendencia']:>6.3f} {sig_symbol:>3s} "
              f"{r['correlacao_abs']:>8.3f} {r['volatilidade']:>6.2f}% "
              f"{vol_medio_m:>10.1f}M")

    # Estatísticas gerais
    print("\n" + "="*120)
    print("📊 ESTATÍSTICAS GERAIS")
    print("="*120)

    correlacoes_tend = [r['correlacao_tendencia'] for r in resultados]
    correlacoes_abs = [r['correlacao_abs'] for r in resultados]
    significativos = sum([r['significativo_tendencia'] for r in resultados])

    print(f"Correlação Volume vs Tendência:")
    print(f"  Média: {np.mean(correlacoes_tend):+.3f}")
    print(f"  Mediana: {np.median(correlacoes_tend):+.3f}")
    print(f"  Desvio Padrão: {np.std(correlacoes_tend):.3f}")
    print(f"  Mín/Máx: {np.min(correlacoes_tend):+.3f} / {np.max(correlacoes_tend):+.3f}")

    print(f"\nCorrelação Volume vs |Tendência|:")
    print(f"  Média: {np.mean(correlacoes_abs):+.3f}")
    print(f"  Mediana: {np.median(correlacoes_abs):+.3f}")
    print(f"  Desvio Padrão: {np.std(correlacoes_abs):.3f}")
    print(f"  Mín/Máx: {np.min(correlacoes_abs):+.3f} / {np.max(correlacoes_abs):+.3f}")

    print(f"\nSignificância Estatística:")
    print(f"  Correlações significativas (p < 0.05): {significativos}/{len(resultados)} ({significativos/len(resultados)*100:.1f}%)")

    # Análise por categorias
    print(f"\n🎯 ANÁLISE POR CATEGORIAS")
    print("="*120)

    # Categorizar por força da correlação absoluta
    forte_pos = [r for r in resultados if r['correlacao_abs'] > 0.3]
    moderada_pos = [r for r in resultados if 0.1 <= r['correlacao_abs'] <= 0.3]
    fraca = [r for r in resultados if r['correlacao_abs'] < 0.1]

    print(f"Correlação Volume-|Tendência| FORTE (>0.3):     {len(forte_pos):2d} ações")
    print(f"Correlação Volume-|Tendência| MODERADA (0.1-0.3): {len(moderada_pos):2d} ações")
    print(f"Correlação Volume-|Tendência| FRACA (<0.1):      {len(fraca):2d} ações")

    if forte_pos:
        print(f"\nAções com correlação FORTE:")
        for r in forte_pos:
            print(f"  • {r['ticker'].replace('.SA', ''):<6} - {r['nome']:<20} | Corr: {r['correlacao_abs']:+.3f}")

def main():
    print("🚀 ANÁLISE CORRELAÇÃO VOLUME vs TENDÊNCIA - AÇÕES DIVERSIFICADAS")
    print("="*80)
    print("📊 Análise de Correlação entre:")
    print("   • Volume de negociação")
    print("   • Tendência diária (variação % do preço)")
    print("   • Tendência absoluta (|variação %|)")
    print("   • Variação absoluta de preço (R$)")
    print("   • Volatilidade e estatísticas")

    # Carregar ações do arquivo CSV
    acoes = carregar_acoes_diversificadas()

    if not acoes:
        print("❌ Não foi possível carregar as ações do arquivo CSV")
        return

    print(f"\n📋 Serão analisadas {len(acoes)} ações diversificadas")
    print("⏱️  Tempo estimado: 3-5 minutos")

    confirma = input("\nDeseja continuar? (s/N): ").strip().lower()
    if confirma != 's':
        print("❌ Cancelado pelo usuário")
        return

    print(f"\n📈 Iniciando análise de correlações...")

    resultados = []
    sucesso = 0
    erro = 0

    for i, (ticker, nome) in enumerate(acoes, 1):
        print(f"\n[{i:2d}/{len(acoes)}]", end=" ")

        dados = obter_dados_volume_tendencia(ticker, nome)

        if dados is not None:
            correlacoes = calcular_correlacoes(dados, ticker, nome)
            resultado = criar_grafico_correlacao_individual(dados, ticker, nome, correlacoes)
            resultados.append(resultado)
            sucesso += 1
        else:
            erro += 1

    print(f"\n✅ Processamento concluído!")
    print(f"   Sucessos: {sucesso}")
    print(f"   Erros: {erro}")

    if resultados:
        # Criar visualizações consolidadas
        criar_dashboard_correlacoes(resultados)
        criar_heatmap_correlacoes(resultados)

        # Gerar relatório
        gerar_relatorio_correlacoes(resultados)

        # Salvar resultados em CSV
        try:
            df_resultados = pd.DataFrame(resultados)
            os.makedirs('results/csv/volume_tendencia', exist_ok=True)
            csv_path = 'results/csv/volume_tendencia/correlacoes_volume_tendencia.csv'
            df_resultados.to_csv(csv_path, index=False)
            print(f"\n📁 ARQUIVOS GERADOS:")
            print(f"   • {sucesso} gráficos individuais de correlação")
            print(f"   • 1 dashboard consolidado")
            print(f"   • 1 heatmap de correlações")
            print(f"   • Resultados salvos em: {csv_path}")
        except Exception as e:
            print(f"   ⚠️ Erro ao salvar CSV: {e}")

    else:
        print("\n❌ Nenhuma análise foi gerada!")

if __name__ == "__main__":
    main()
